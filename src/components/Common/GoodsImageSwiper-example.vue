<template>
  <div class="example-container">
    <h2>图片轮播组件使用示例</h2>
    
    <!-- 详情页模式示例 -->
    <div class="example-section">
      <h3>详情页模式 (mode="detail")</h3>
      <GoodsImageSwiper
        :mediaList="detailImages"
        mode="detail"
        :autoplay="true"
        :loop="true"
        paginationType="dots"
        :showPagination="true"
        @imageClick="handleImageClick"
        @slideChange="handleSlideChange"
        @linkClick="handleLinkClick"
      />
    </div>

    <!-- 长条模式示例 -->
    <div class="example-section">
      <h3>长条模式 (mode="banner")</h3>
      <GoodsImageSwiper
        :mediaList="bannerImages"
        mode="banner"
        :autoplay="true"
        :loop="true"
        paginationType="fraction"
        :showPagination="true"
        @imageClick="handleImageClick"
        @slideChange="handleSlideChange"
        @linkClick="handleLinkClick"
      />
    </div>

    <!-- 自定义分页器示例 -->
    <div class="example-section">
      <h3>自定义分页器</h3>
      <GoodsImageSwiper
        :mediaList="customImages"
        mode="detail"
        :autoplay="false"
        :loop="true"
        paginationType="none"
        :showPagination="true"
        @imageClick="handleImageClick"
        @slideChange="handleSlideChange"
      >
        <template #pagination="{ currentIndex, total, slideTo }">
          <div class="custom-pagination-example">
            <button 
              v-for="(_, index) in total" 
              :key="index"
              :class="['custom-btn', { active: currentIndex === index }]"
              @click="slideTo(index)"
            >
              {{ index + 1 }}
            </button>
          </div>
        </template>
      </GoodsImageSwiper>
    </div>

    <!-- 事件日志 -->
    <div class="event-log">
      <h3>事件日志</h3>
      <div class="log-content">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import GoodsImageSwiper from './GoodsImageSwiper.vue'

// 事件日志
const eventLogs = ref([])

// 详情页模式图片数据
const detailImages = ref([
  {
    type: 'image',
    url: 'https://picsum.photos/800/600?random=1',
    alt: '商品图片1',
    linkUrl: 'https://example.com/product1'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/600/800?random=2',
    alt: '商品图片2',
    linkUrl: 'https://example.com/product2'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/800/800?random=3',
    alt: '商品图片3',
    linkUrl: 'https://example.com/product3'
  }
])

// 长条模式图片数据
const bannerImages = ref([
  {
    type: 'image',
    url: 'https://picsum.photos/1200/400?random=4',
    alt: 'Banner图片1',
    linkUrl: 'https://example.com/banner1'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/1200/400?random=5',
    alt: 'Banner图片2',
    linkUrl: 'https://example.com/banner2'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/1200/400?random=6',
    alt: 'Banner图片3',
    linkUrl: 'https://example.com/banner3'
  }
])

// 自定义分页器图片数据
const customImages = ref([
  {
    type: 'image',
    url: 'https://picsum.photos/800/600?random=7',
    alt: '自定义图片1'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/600/800?random=8',
    alt: '自定义图片2'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/800/800?random=9',
    alt: '自定义图片3'
  },
  {
    type: 'image',
    url: 'https://picsum.photos/1000/600?random=10',
    alt: '自定义图片4'
  }
])

// 事件处理函数
const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  eventLogs.value.unshift(`[${timestamp}] ${message}`)
  if (eventLogs.value.length > 10) {
    eventLogs.value.pop()
  }
}

const handleImageClick = ({ item, index }) => {
  addLog(`图片点击: 索引 ${index}, URL: ${item.url}`)
}

const handleSlideChange = (index) => {
  addLog(`幻灯片切换: 当前索引 ${index}`)
}

const handleLinkClick = (linkUrl) => {
  addLog(`链接点击: ${linkUrl}`)
}
</script>

<style scoped lang="less">
.example-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  h2 {
    text-align: center;
    margin-bottom: 40px;
    color: #333;
  }

  .example-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;

    h3 {
      margin-bottom: 20px;
      color: #555;
      font-size: 18px;
    }
  }

  .custom-pagination-example {
    display: flex;
    justify-content: center;
    gap: 8px;
    padding: 12px;

    .custom-btn {
      padding: 8px 12px;
      border: 1px solid #ddd;
      background: #fff;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f5f5f5;
        border-color: #ff7a0a;
      }

      &.active {
        background: #ff7a0a;
        color: #fff;
        border-color: #ff7a0a;
      }
    }
  }

  .event-log {
    margin-top: 40px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #f9f9f9;

    h3 {
      margin-bottom: 15px;
      color: #555;
    }

    .log-content {
      max-height: 200px;
      overflow-y: auto;
      font-family: monospace;
      font-size: 12px;

      .log-item {
        padding: 4px 0;
        border-bottom: 1px solid #eee;
        color: #666;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .example-container {
    padding: 10px;

    .example-section {
      padding: 15px;
    }
  }
}
</style>
