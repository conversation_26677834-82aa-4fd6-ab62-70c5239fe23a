# GoodsImageSwiper 图片轮播组件

基于 swiper/vue 的高性能图片轮播组件，专为商品展示和横幅广告设计。

## 特性

- ✅ **纯图片轮播** - 去除所有视频相关逻辑，专注图片展示
- ✅ **高性能优化** - 图片懒加载、预加载、内存管理
- ✅ **响应式设计** - 支持桌面端和移动端
- ✅ **双模式支持** - 详情页模式和横幅模式
- ✅ **智能图片适配** - 根据宽高比自动适配长图和方图
- ✅ **灵活分页器** - 支持圆点、分数和自定义分页器
- ✅ **链接跳转** - 支持图片点击跳转
- ✅ **TypeScript 友好** - 完整的类型定义

## 安装依赖

```bash
npm install swiper
```

## 基础用法

```vue
<template>
  <GoodsImageSwiper
    :mediaList="imageList"
    mode="detail"
    :autoplay="true"
    :loop="true"
    paginationType="dots"
    @imageClick="handleImageClick"
  />
</template>

<script setup>
import GoodsImageSwiper from '@/components/Common/GoodsImageSwiper.vue'

const imageList = [
  {
    type: 'image',
    url: 'https://example.com/image1.jpg',
    alt: '商品图片1',
    linkUrl: 'https://example.com/product1'
  },
  {
    type: 'image',
    url: 'https://example.com/image2.jpg',
    alt: '商品图片2',
    linkUrl: 'https://example.com/product2'
  }
]

const handleImageClick = ({ item, index }) => {
  console.log('图片点击:', item, index)
}
</script>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `mediaList` | `Array` | `[]` | 图片数据数组 |
| `autoplay` | `Boolean` | `false` | 是否自动播放 |
| `loop` | `Boolean` | `true` | 是否循环播放 |
| `mode` | `String` | `'detail'` | 显示模式：`'detail'` 详情页模式, `'banner'` 横幅模式 |
| `height` | `String/Number` | `null` | 自定义高度 |
| `paginationType` | `String` | `'dots'` | 分页器类型：`'dots'` 圆点, `'fraction'` 分数, `'none'` 不显示 |
| `showPagination` | `Boolean` | `true` | 是否显示分页器 |
| `autoplayDelay` | `Number` | `3000` | 自动播放延迟时间（毫秒） |

## 数据格式

```javascript
const mediaList = [
  {
    type: 'image',        // 必须为 'image'
    url: 'image_url',     // 图片URL，必填
    alt: 'image_alt',     // 图片alt属性，可选
    linkUrl: 'link_url'   // 点击跳转链接，可选
  }
]
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `imageClick` | `{ item, index }` | 图片点击事件 |
| `slideChange` | `index` | 幻灯片切换事件 |
| `linkClick` | `linkUrl` | 链接点击事件 |

## Slots

### pagination

自定义分页器插槽

```vue
<GoodsImageSwiper :mediaList="imageList">
  <template #pagination="{ currentIndex, total, slideTo }">
    <div class="custom-pagination">
      <button 
        v-for="(_, index) in total" 
        :key="index"
        :class="{ active: currentIndex === index }"
        @click="slideTo(index)"
      >
        {{ index + 1 }}
      </button>
    </div>
  </template>
</GoodsImageSwiper>
```

## 模式说明

### 详情页模式 (mode="detail")

- 移动端：单张图片显示
- 桌面端：2.2张图片显示，居中对齐，非激活图片缩放效果
- 适合商品详情页使用

### 横幅模式 (mode="banner")

- 始终单张图片显示
- 图片自适应高度
- 适合首页横幅广告使用

## 图片适配规则

组件会根据图片宽高比自动应用不同的样式类：

- `image-landscape`: 宽高比 > 1.2 的横向图片
- `image-portrait`: 宽高比 < 0.8 的纵向图片  
- `image-square`: 宽高比在 0.8-1.2 之间的方形图片
- `image-banner`: 横幅模式下的图片
- `image-desktop`: 桌面端图片

## 性能优化

1. **图片懒加载**: 前3张图片立即加载，其余懒加载
2. **预加载优化**: 第一张图片高优先级加载
3. **内存管理**: 组件销毁时自动清理 Swiper 实例
4. **响应式优化**: 窗口大小变化时自动调整配置

## 样式自定义

组件使用 CSS 变量，可以通过覆盖变量来自定义样式：

```css
.image-swiper {
  --swiper-theme-color: #ff7a0a; /* 主题色 */
  --pagination-dot-size: 6px;    /* 分页器圆点大小 */
  --loading-spinner-color: #ff7a0a; /* 加载动画颜色 */
}
```

## 注意事项

1. 确保 `mediaList` 中每个项目都包含 `type: 'image'` 和有效的 `url`
2. 组件会自动处理图片加载失败的情况
3. 在 SSR 环境中使用时，注意 `window` 对象的可用性
4. 建议为图片提供合适的 `alt` 属性以提高可访问性

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- iOS Safari 12+
- Android Chrome 60+
