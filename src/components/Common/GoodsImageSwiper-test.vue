<template>
  <div class="test-container">
    <h1>图片轮播组件测试</h1>
    
    <!-- 基础测试 -->
    <div class="test-section">
      <h2>基础功能测试</h2>
      <GoodsImageSwiper
        :mediaList="testImages"
        mode="detail"
        :autoplay="false"
        :loop="true"
        paginationType="dots"
        :showPagination="true"
        @imageClick="onImageClick"
        @slideChange="onSlideChange"
        @linkClick="onLinkClick"
      />
    </div>

    <!-- 横幅模式测试 -->
    <div class="test-section">
      <h2>横幅模式测试</h2>
      <GoodsImageSwiper
        :mediaList="bannerImages"
        mode="banner"
        :autoplay="true"
        :autoplayDelay="2000"
        :loop="true"
        paginationType="fraction"
        :showPagination="true"
      />
    </div>

    <!-- 测试日志 -->
    <div class="test-logs">
      <h3>测试日志</h3>
      <div class="logs">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import GoodsImageSwiper from './GoodsImageSwiper.vue'

const logs = ref([])

// 测试图片数据
const testImages = ref([
  {
    type: 'image',
    url: 'https://via.placeholder.com/800x600/FF6B6B/FFFFFF?text=Image+1',
    alt: '测试图片1',
    linkUrl: 'https://example.com/1'
  },
  {
    type: 'image',
    url: 'https://via.placeholder.com/600x800/4ECDC4/FFFFFF?text=Image+2',
    alt: '测试图片2',
    linkUrl: 'https://example.com/2'
  },
  {
    type: 'image',
    url: 'https://via.placeholder.com/800x800/45B7D1/FFFFFF?text=Image+3',
    alt: '测试图片3',
    linkUrl: 'https://example.com/3'
  }
])

const bannerImages = ref([
  {
    type: 'image',
    url: 'https://via.placeholder.com/1200x400/FF6B6B/FFFFFF?text=Banner+1',
    alt: '横幅图片1',
    linkUrl: 'https://example.com/banner1'
  },
  {
    type: 'image',
    url: 'https://via.placeholder.com/1200x400/4ECDC4/FFFFFF?text=Banner+2',
    alt: '横幅图片2',
    linkUrl: 'https://example.com/banner2'
  }
])

const addLog = (message) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

const onImageClick = ({ item, index }) => {
  addLog(`图片点击 - 索引: ${index}, URL: ${item.url}`)
}

const onSlideChange = (index) => {
  addLog(`幻灯片切换 - 当前索引: ${index}`)
}

const onLinkClick = (linkUrl) => {
  addLog(`链接点击 - URL: ${linkUrl}`)
}
</script>

<style scoped lang="less">
.test-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;

  h1 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
  }

  .test-section {
    margin-bottom: 40px;
    padding: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background: #fff;

    h2 {
      margin-bottom: 20px;
      color: #555;
      font-size: 18px;
    }
  }

  .test-logs {
    margin-top: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;

    h3 {
      margin-bottom: 15px;
      color: #333;
    }

    .logs {
      max-height: 300px;
      overflow-y: auto;
      font-family: 'Courier New', monospace;
      font-size: 12px;

      .log-item {
        padding: 5px 0;
        border-bottom: 1px solid #ddd;
        color: #666;

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
